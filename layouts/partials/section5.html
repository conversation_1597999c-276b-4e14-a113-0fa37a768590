{{- $section5 := .Site.Params.section5 }} {{- if eq $section5 true }} {{-
$section5 = dict "_" "_" }} {{- end }} {{- $title := index $section5 "title" |
default "Drop us a line or two "}} {{- $subtitle := index $section5 "subtitle" |
default "We'd love to hear from you" }} {{- $action := index $section5 "action"
}} {{- $method := index $section5 "method" }} {{- $buttonText := index $section5
"buttontext" | default "Send Message" }} {{- $recaptchaSiteKey := index
$section5 "recaptcha_site_key" }}
<!-- Load reCAPTCHA script -->
{{- if $recaptchaSiteKey }}
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<style>
  .grecaptcha-badge {
    visibility: hidden;
  }
</style>
{{- end }}

<section class="section section-light-grey is-medium" id="section5">
  <div class="container">
    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2 is-spaced">{{ $title }}</h2>
      <h3 class="subtitle is-5 is-muted">{{ $subtitle }}</h3>
      <div class="divider is-centered"></div>
    </div>

    <div class="content-wrapper">
      <div class="columns">
        <div class="column is-6 is-offset-3">
          <!-- Success Message (initially hidden) -->
          <div
            id="form-success"
            class="notification is-success"
            style="display: none"
          >
            <button
              class="delete"
              onclick="document.getElementById('form-success').style.display='none'"
            ></button>
            <strong>Thank you!</strong> Your message has been sent successfully.
            We'll get back to you soon.
          </div>

          <!-- Error Message (initially hidden) -->
          <div
            id="form-error"
            class="notification is-danger"
            style="display: none"
          >
            <button
              class="delete"
              onclick="document.getElementById('form-error').style.display='none'"
            ></button>
            There was a problem submitting your form. Please try again.
          </div>

          <form
            id="contact-form"
            {{
            with
            $action
            }}
            action="{{ . }}"
            {{end}}{{
            with
            $method
            }}
            method="{{ . }}"
            {{end}}
          >
            <div class="columns is-multiline">
              <div class="column is-6">
                <input
                  class="input is-medium"
                  name="name"
                  type="text"
                  placeholder="Enter your name"
                  required
                />
              </div>
              <div class="column is-6">
                <input
                  class="input is-medium"
                  name="email"
                  type="email"
                  placeholder="Enter your email address"
                  required
                />
              </div>
              <div class="column is-12">
                <input
                  class="input is-medium"
                  name="subject"
                  type="text"
                  placeholder="Subject"
                />
              </div>
              <div class="column is-12">
                <textarea
                  class="textarea"
                  name="message"
                  rows="10"
                  placeholder="Write something..."
                  required
                ></textarea>
              </div>
              <!-- Formspree honeypot field to prevent spam -->
              <input type="text" name="_gotcha" style="display: none" />
              <div class="form-footer has-text-centered mt-10">
                {{- if $recaptchaSiteKey }}
                <button
                  id="submit-button"
                  class="button cta is-large primary-btn raised is-clear g-recaptcha"
                  data-sitekey="{{ $recaptchaSiteKey }}"
                  data-callback="onSubmit"
                >
                  {{ $buttonText }}
                </button>
                {{- else }}
                <button
                  id="submit-button"
                  type="submit"
                  class="button cta is-large primary-btn raised is-clear"
                >
                  {{ $buttonText }}
                </button>
                {{- end }}
              </div>
              {{- if $recaptchaSiteKey }}
              <div class="column is-12 has-text-centered">
                <p class="is-size-7 has-text-grey">
                  This site is protected by reCAPTCHA and the Google
                  <a href="https://policies.google.com/privacy" target="_blank">Privacy Policy</a> and
                  <a href="https://policies.google.com/terms" target="_blank">Terms of Service</a> apply.
                </p>
              </div>
              {{- end }}
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  var form, submitButton, successMessage, errorMessage, hasRecaptcha

  document.addEventListener("DOMContentLoaded", function () {
    form = document.getElementById("contact-form");
    submitButton = document.getElementById("submit-button");
    successMessage = document.getElementById("form-success");
    errorMessage = document.getElementById("form-error");
    hasRecaptcha = submitButton.classList.contains('g-recaptcha');

    // Only add submit event listener if no reCAPTCHA (for fallback)
    if (!hasRecaptcha) {
      form.addEventListener("submit", handleSubmit);
    }
  });

  async function submitForm() {
    // Hide any previous messages
    successMessage.style.display = "none";
    errorMessage.style.display = "none";

    // Change button text to show loading state
    var originalButtonText = submitButton.textContent;
    submitButton.textContent = "Sending...";
    submitButton.disabled = true;

    // Create form data from the form element
    var data = new FormData(form);

    try {
      console.log("Submitting form data:", data);
      console.log("Form action:", form.action);

      const response = await fetch(form.action, {
        method: form.method,
        body: data,
        headers: {
          Accept: "application/json",
        },
      });

      console.log("Response status:", response.status);

      if (response.ok) {
        successMessage.style.display = "block";
        form.reset();
        // Scroll to success message
        successMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      } else {
        // Log the error response for debugging
        const errorData = await response.text();
        console.error("Form submission error:", response.status, errorData);
        errorMessage.style.display = "block";
        errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    } catch (error) {
      console.error("Network error:", error);
      errorMessage.style.display = "block";
      errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    // Reset button state
    submitButton.textContent = originalButtonText;
    submitButton.disabled = false;
  }

  function handleSubmit(event) {
    event.preventDefault();

    // Validate form first
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }

    console.log("No reCAPTCHA, submitting directly...");
    // No reCAPTCHA, submit directly
    submitForm();
  }

  // Global function for reCAPTCHA callback (required by Formspree)
  function onSubmit(token) {
    // This function is called when reCAPTCHA is successfully completed
    console.log("reCAPTCHA success, token received:", token);
    // Validate form before submitting
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }
    // Submit the form - the reCAPTCHA token is automatically included by the reCAPTCHA library
    submitForm();
  }
</script>
