{{- $section5 := .Site.Params.section5 }} {{- if eq $section5 true }} {{-
$section5 = dict "_" "_" }} {{- end }} {{- $title := index $section5 "title" |
default "Drop us a line or two "}} {{- $subtitle := index $section5 "subtitle" |
default "We'd love to hear from you" }} {{- $action := index $section5 "action"
}} {{- $method := index $section5 "method" }} {{- $buttonText := index $section5
"buttontext" | default "Send Message" }} {{- $recaptchaSiteKey := index
$section5 "recaptcha_site_key" }}
<!-- Load reCAPTCHA script -->
{{- if $recaptchaSiteKey }}
<script src="https://www.google.com/recaptcha/api.js?onload=onRecaptchaReady&render=explicit" async defer></script>
{{- end }}

<section class="section section-light-grey is-medium" id="section5">
  <div class="container">
    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2 is-spaced">{{ $title }}</h2>
      <h3 class="subtitle is-5 is-muted">{{ $subtitle }}</h3>
      <div class="divider is-centered"></div>
    </div>

    <div class="content-wrapper">
      <div class="columns">
        <div class="column is-6 is-offset-3">
          <!-- Success Message (initially hidden) -->
          <div
            id="form-success"
            class="notification is-success"
            style="display: none"
          >
            <button
              class="delete"
              onclick="document.getElementById('form-success').style.display='none'"
            ></button>
            <strong>Thank you!</strong> Your message has been sent successfully.
            We'll get back to you soon.
          </div>

          <!-- Error Message (initially hidden) -->
          <div
            id="form-error"
            class="notification is-danger"
            style="display: none"
          >
            <button
              class="delete"
              onclick="document.getElementById('form-error').style.display='none'"
            ></button>
            There was a problem submitting your form. Please try again.
          </div>

          <form
            id="contact-form"
            {{
            with
            $action
            }}
            action="{{ . }}"
            {{end}}{{
            with
            $method
            }}
            method="{{ . }}"
            {{end}}
          >
            <div class="columns is-multiline">
              <div class="column is-6">
                <input
                  class="input is-medium"
                  name="name"
                  type="text"
                  placeholder="Enter your name"
                  required
                />
              </div>
              <div class="column is-6">
                <input
                  class="input is-medium"
                  name="email"
                  type="email"
                  placeholder="Enter your email address"
                  required
                />
              </div>
              <div class="column is-12">
                <input
                  class="input is-medium"
                  name="subject"
                  type="text"
                  placeholder="Subject"
                />
              </div>
              <div class="column is-12">
                <textarea
                  class="textarea"
                  name="message"
                  rows="10"
                  placeholder="Write something..."
                  required
                ></textarea>
              </div>
              <!-- Formspree honeypot field to prevent spam -->
              <input type="text" name="_gotcha" style="display: none" />
              <div class="form-footer has-text-centered mt-10">
                <button
                  id="submit-button"
                  type="submit"
                  class="button cta is-large primary-btn raised is-clear"
                >
                  {{ $buttonText }}
                </button>
                <!-- Hidden reCAPTCHA widget (will be rendered programmatically) -->
                {{- if $recaptchaSiteKey }}
                <div id="recaptcha-widget"></div>
                {{- end }}
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  var form, submitButton, successMessage, errorMessage, recaptchaWidget, hasRecaptcha, recaptchaWidgetId

  document.addEventListener("DOMContentLoaded", function () {
    form = document.getElementById("contact-form");
    submitButton = document.getElementById("submit-button");
    successMessage = document.getElementById("form-success");
    errorMessage = document.getElementById("form-error");
    recaptchaWidget = document.getElementById("recaptcha-widget");
    hasRecaptcha = recaptchaWidget !== null;
    recaptchaWidgetId = null;

    // Always add the submit event listener
    form.addEventListener("submit", handleSubmit);

    // Initialize reCAPTCHA when the API is ready
    if (hasRecaptcha) {
      window.onRecaptchaReady = function() {
        if (typeof grecaptcha !== "undefined") {
          // Get the widget ID for invisible reCAPTCHA
          recaptchaWidgetId = grecaptcha.render(recaptchaWidget, {
            'sitekey': '{{ $recaptchaSiteKey }}',
            'callback': onRecaptchaSuccess,
            'size': 'invisible'
          });
        }
      };

      // If grecaptcha is already loaded, initialize immediately
      if (typeof grecaptcha !== "undefined") {
        window.onRecaptchaReady();
      }
    }
  });

  async function submitFormWithRecaptcha(recaptchaToken) {
    // Hide any previous messages
    successMessage.style.display = "none";
    errorMessage.style.display = "none";

    // Change button text to show loading state
    var originalButtonText = submitButton.textContent;
    submitButton.textContent = "Sending...";
    submitButton.disabled = true;

    // Create form data from the form element (not event.target)
    var data = new FormData(form);

    // Add reCAPTCHA token if provided
    if (recaptchaToken) {
      data.append("g-recaptcha-response", recaptchaToken);
    }

    try {
      console.log("Submitting form data:", data);
      console.log("Form action:", form.action);

      const response = await fetch(form.action, {
        method: form.method,
        body: data,
        headers: {
          Accept: "application/json",
        },
      });

      console.log("Response status:", response.status);

      if (response.ok) {
        successMessage.style.display = "block";
        form.reset();
        // Reset reCAPTCHA if it exists
        if (hasRecaptcha && typeof grecaptcha !== "undefined" && recaptchaWidgetId !== null) {
          grecaptcha.reset(recaptchaWidgetId);
        }
        // Scroll to success message
        successMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      } else {
        // Log the error response for debugging
        const errorData = await response.text();
        console.error("Form submission error:", response.status, errorData);
        errorMessage.style.display = "block";
        errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
        // Reset reCAPTCHA on error so user can try again
        if (hasRecaptcha && typeof grecaptcha !== "undefined" && recaptchaWidgetId !== null) {
          grecaptcha.reset(recaptchaWidgetId);
        }
      }
    } catch (error) {
      console.error("Network error:", error);
      errorMessage.style.display = "block";
      errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      // Reset reCAPTCHA on network error so user can try again
      if (hasRecaptcha && typeof grecaptcha !== "undefined" && recaptchaWidgetId !== null) {
        grecaptcha.reset(recaptchaWidgetId);
      }
    }

    // Reset button state
    submitButton.textContent = originalButtonText;
    submitButton.disabled = false;
  }

  function handleSubmit(event) {
    event.preventDefault();

    // Validate form first
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }

    if (hasRecaptcha && typeof grecaptcha !== "undefined" && recaptchaWidgetId !== null) {
      // Trigger reCAPTCHA with the specific widget ID
      console.log("Triggering reCAPTCHA...");
      grecaptcha.execute(recaptchaWidgetId);
    } else {
      console.log("No reCAPTCHA, submitting directly...");
      // No reCAPTCHA, submit directly
      submitFormWithRecaptcha();
    }
  }

  // Global function for reCAPTCHA callback
  function onRecaptchaSuccess(token) {
    // This function is called when reCAPTCHA is successfully completed
    console.log("reCAPTCHA success, token received:", token);
    submitFormWithRecaptcha(token);
  }
</script>
