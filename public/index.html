<!DOCTYPE html>
<html lang="en-us">
  <head>
	<meta name="generator" content="Hugo 0.147.5"><script src="/livereload.js?mindelay=10&amp;v=2&amp;port=1313&amp;path=livereload" data-no-instant defer></script>
    

<meta property="og:url" content="http://localhost:1313/">
  <meta property="og:site_name" content="whitesky cloud platform">
  <meta property="og:title" content="whitesky cloud platform">
  <meta property="og:locale" content="en_us">
  <meta property="og:type" content="website">


<meta name="description" content="Hardcoded description; the author should update :)" />
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>whitesky cloud platform</title>
    
  
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
  integrity="sha512-Avb2QiuDEEvB4bZJYdft2mNjVShBftLdPG8FJ0V7irTLQ8Uo0qcPxh4Plq7G5tGm0rU+1SPhVotteLpBERwTkw=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
/>
<link rel="icon" type="image/png" href="/images/favicon.png" />
<link href="https://fonts.googleapis.com/css?family=Open&#43;Sans:400,600" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/css/style.css">
<link rel="stylesheet" type="text/css" href="/css/icons.css">

  </head>
  <body>
    
    
    <div id="preloader">
      <div id="status"></div>
    </div>
    

    

    
    <section class="hero is-fullheight is-default is-bold">
  
<nav class="navbar is-fresh is-transparent no-shadow" role="navigation" aria-label="main navigation">
  <div class="container">
    <div class="navbar-brand">
      <a class="navbar-item" href="/">
        <img src="/images/logo-blue-blacktext-small.png" alt="" width="160" height="28">
      </a>

      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbar-menu">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>

      <div id="navbar-menu" class="navbar-menu is-static">

        <div class="navbar-end">
          <a href="/" class="navbar-item is-secondary">
            Features
          </a>
          <a href="/" class="navbar-item is-secondary">
            Pricing
          </a>
          <div class="navbar-item has-dropdown is-hoverable">
            <a class="navbar-link">
              Dropdown
            </a>

            <div class="navbar-dropdown">
              <a href="/" class="navbar-item">
                Dropdown item
              </a>
              <a href="/" class="navbar-item">
                Dropdown item
              </a>
              <a href="/" class="navbar-item">
                Dropdown item
              </a>
            </div>
          </div>
          <a href="/" class="navbar-item is-secondary">
            Log in
          </a>
          <a href="/" class="navbar-item">
            <span class="button signup-button rounded secondary-btn raised">
              Try now
            </span>
          </a>
        </div>
      </div>
  </div>
</nav>

  
<nav id="navbar-clone" class="navbar is-fresh is-transparent" role="navigation" aria-label="main navigation">
  <div class="container">
    <div class="navbar-brand">
      <a class="navbar-item" href="/">
        <img src="/images/logo-blue-blacktext-small.png" alt="" width="160" height="160">
      </a>

      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="cloned-navbar-menu">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>

    <div id="cloned-navbar-menu" class="navbar-menu is-fixed">

      <div class="navbar-end">
        <a href="/" class="navbar-item is-secondary">
          Features
        </a>
        <a href="/" class="navbar-item is-secondary">
          Pricing
        </a>
        <div class="navbar-item has-dropdown is-hoverable">
          <a class="navbar-link">
            Dropdown
          </a>

          <div class="navbar-dropdown">
            <a href="/" class="navbar-item">
              Dropdown item
            </a>
            <a href="/" class="navbar-item">
              Dropdown item
            </a>
            <a href="/" class="navbar-item">
              Dropdown item
            </a>
          </div>
        </div>
        <a href="/" class="navbar-item is-secondary">
          Log in
        </a>
        <a href="/" class="navbar-item">
          <span class="button signup-button rounded secondary-btn raised">
            Try now
          </span>
        </a>
      </div>
    </div>
</div>
</nav>

  
<div class="hero-body">
  <div class="container">
    <div class="columns is-vcentered">
      <div class="column is-5 is-offset-1 landing-caption">
        <h1 class="title is-1 is-bold is-spaced">
          The most complete cloud stack, delivered as-a-service
        </h1>
        
        <h2 class="subtitle is-5 is-muted">
          Built on Linux and the KVM hypervisor. Every component — from the virtual machine orchestration layer and the software defined storage to the billing engine — is engineered and maintained by whitesky.
        </h2>
        
        <p>
          <a class="button cta rounded primary-btn raised" href="#">
            Try now
          </a>
        </p>
      </div>
      <div class="column is-5 is-offset-1">
        <figure class="image is-4by3">
          <img src="/images/cloud-worker-small.jpg" alt="Description">
        </figure>
      </div>
    </div>
  </div>
</div>
  
<div class="hero-foot mb-20">
  <div class="container">
    <div class="tabs is-centered">
      <ul>
        <li>
          <a  href="https://www.americamovil.com/" >
            <img class="partner-logo" src="/images/logos/clients/america-movil-gray.svg">
          </a>
        </li>
        <li>
          <a  href="https://circles.co" >
            <img class="partner-logo" src="/images/logos/clients/circlesco-logo-2.svg">
          </a>
        </li>
        <li>
          <a  href="https://exalate.com" >
            <img class="partner-logo" src="/images/logos/clients/exalate.svg">
          </a>
        </li>
        <li>
          <a  href="https://www.lealgroup.mu" >
            <img class="partner-logo" src="/images/logos/clients/leal.svg">
          </a>
        </li>
        <li>
          <a  href="https://www.varity.nl" >
            <img class="partner-logo" src="/images/logos/clients/varity.svg">
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>
</section>
    

    
    
<section class="section section-feature-grey is-medium" id="section1">
  <div class="container">
    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2">The alternative providing sovereignty with comfort</h2>
      <h3 class="subtitle is-5 is-muted">It feels like cloud, but it runs on your hardware in your (colo) datacenter.</h3>
      <div class="divider is-centered"></div>
    </div>

    <div class="content-wrapper">
      <div class="columns">
        <div class="column is-one-third">
          <div class="feature-card is-bordered has-text-centered revealOnScroll delay-1" data-animation="fadeInLeft">
            <div class="card-title">
              <h4>Complete Cloud Environment</h4>
            </div>
            <div class="card-icon">
                <i class="fas fa-cloud primary-color" style="font-size:47px; margin-top: 20px; margin-bottom: 20px"></i>
            </div>
            <div class="card-text">
                <p>The European answer to VMware, Nutanix, OpenStack and others — built for flexibility, sovereignty, performance and cooperation.</p>
            </div>
            
          </div>
        </div>
        <div class="column is-one-third">
          <div class="feature-card is-bordered has-text-centered revealOnScroll delay-1" data-animation="fadeInLeft">
            <div class="card-title">
              <h4>Seamless Transition</h4>
            </div>
            <div class="card-icon">
                <i class="fas fa-right-left primary-color" style="font-size:47px; margin-top: 20px; margin-bottom: 20px"></i>
            </div>
            <div class="card-text">
                <p>Migrate from legacy systems or US-based hyperscalers with minimal disruption — whitesky.cloud offers a smooth, sovereign path forward.</p>
            </div>
            
          </div>
        </div>
        <div class="column is-one-third">
          <div class="feature-card is-bordered has-text-centered revealOnScroll delay-1" data-animation="fadeInLeft">
            <div class="card-title">
              <h4>Customized Deployment</h4>
            </div>
            <div class="card-icon">
                <i class="fas fa-tools primary-color" style="font-size:47px; margin-top: 20px; margin-bottom: 20px"></i>
            </div>
            <div class="card-text">
                <p>Unlike one-size-fits-all hyperscalers, we tailor deployments to your specific needs — ensuring full alignment with your organization and compliance requirements.</p>
            </div>
            
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
    

    
    
<section class="section is-medium" id="section2">
  <div class="container">
    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2">A Vanilla Cloud Stack</h2>
      <h3 class="subtitle is-5 is-muted">We believe in choice — not lock-in. Our platform earns loyalty through performance, not constraints.</h3>
      <div class="divider is-centered"></div>
    </div>
    
    <div class="columns">
      <div class="column is-6">
        <article class="media icon-box">
          <figure class="media-left">
            <p class="image">
              <i class="fas fa-cloud primary-color" style="font-size:60px; margin-right: 20px;"></i>
            </p>
          </figure>
          <div class="media-content mt-50">
            <div class="content">
              <p>
                <span class="icon-box-title">Cloudspaces</span>

                <span class="icon-box-text"><strong>Fully isolated virtual environments</strong> where you deploy and manage <strong>virtual machines</strong>, VGPUs, <strong>networking</strong>, load balancers, reverse proxies, backups, DNS, SSL certificates, vTPM, secure boot and storage. Cloudspaces give you granular control, anti-affinity policies, and the ability to attach both <strong>out-of-the-box software-defined and direct NVMe storage</strong> to your workloads.</span>
              </p>
            </div>
          </div>
        </article>
        <article class="media icon-box">
          <figure class="media-left">
            <p class="image">
              <i class="fas fa-box primary-color" style="font-size:60px; margin-right: 20px;"></i>
            </p>
          </figure>
          <div class="media-content mt-50">
            <div class="content">
              <p>
                <span class="icon-box-title">Objectspaces</span>

                <span class="icon-box-text"><strong>Scalable, S3-compatible object storage</strong> with versioning and <strong>object locking</strong> built in. Objectspaces allow you to store, protect, and serve unstructured data reliably — whether you&rsquo;re backing up virtual machines, hosting static websites, or integrating with cloud-native apps. A single deployment can scale up to 360PB.</span>
              </p>
            </div>
          </div>
        </article>
      </div>
      <div class="column is-6">
        <article class="media icon-box">
          <figure class="media-left">
            <p class="image">
              <i class="fas fa-cubes primary-color" style="font-size:60px; margin-right: 20px;"></i>
            </p>
          </figure>
          <div class="media-content mt-50">
            <div class="content">
              <p>
                <span class="icon-box-title">Containerspaces</span>

                <span class="icon-box-text"><strong>Fully managed Kubernetes clusters</strong> with built-in multi-site and geo-redundancy capabilities. Containerspaces let you run containerized workloads with seamless integration into the portal, billing, and networking with your virtual machines and storage.</span>
              </p>
            </div>
          </div>
        </article>
        <article class="media icon-box">
          <figure class="media-left">
            <p class="image">
              <i class="fas fa-receipt primary-color" style="font-size:60px; margin-right: 20px;"></i>
            </p>
          </figure>
          <div class="media-content mt-50">
            <div class="content">
              <p>
                <span class="icon-box-title">Billing system</span>

                <span class="icon-box-text"><strong>Built-in billing and invoicing system</strong> designed for MSPs, SaaS providers, and internal IT teams. Whether you&rsquo;re reselling cloud capacity or allocating internal costs, the billing engine supports <strong>usage-based metering, customer invoicing, reseller onboarding, and federation</strong> with other whitesky.cloud providers — all integrated into the platform.</span>
              </p>
            </div>
          </div>
        </article>
      </div>
    </div>
  </div>
</section>
    

    
    
<section class="section section-feature-grey is-medium" id="section3">
  <div class="container">
    <div class="columns">
      <div class="column is-10 is-offset-1">
        <div class="has-text-centered">
          <img class="pushed-image" src="/images/portal.jpg">
        </div>
      </div>
    </div>

    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2">Your cloud. Federated across Europe.</h2>
      <h3 class="subtitle is-5 is-muted">Deploy and scale on infrastructure you own — or across trusted whitesky.cloud locations.</h3>
    </div>

    <p class="has-text-centered mt-20">
      <a class="button cta is-large rounded secondary-btn raised" href="#">
        Try whitesky.cloud
      </a>
    </p>
  </div>
</section>
    

    
    
<section class="section is-medium section-secondary" id="section4">
  <div class="container">

    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2 light-text is-spaced">Trusted by teams building critical infrastructure</h2>
      <h3 class="subtitle is-5 light-text">From telecom to SaaS — see how our clients rely on whitesky.cloud to run and grow with confidence.</h3>
    </div>

    <div class="content-wrapper">
      <div class="columns is-vcentered">
        <div class="column is-4">
          <figure class="testimonial">
            <blockquote>
              whitesky.cloud gives us the flexibility and control to run secure, stable infrastructure across the América Móvil network. The platform is open, customizable, and easy to manage.
            </blockquote>
            <div class="author">
              <img src="/images/illustrations/faces/3.png" alt=""/>
              <h5>Nestor Lopez</h5>
              <span>Senior project manager core networks</span>
            </div>
          </figure>
        </div>
        <div class="column is-4">
          <figure class="testimonial">
            <blockquote>
              With whitesky.cloud, we get a reliable cloud foundation tailored to our needs — fully private, performant, and backed by a team that understands our growth as a SaaS company.
            </blockquote>
            <div class="author">
              <img src="/images/illustrations/faces/2.png" alt=""/>
              <h5>Francis Martens</h5>
              <span>Chief Executive Officer</span>
            </div>
          </figure>
        </div>
        <div class="column is-4">
          <figure class="testimonial">
            <blockquote>
              whitesky.cloud has supported our private cloud journey for years, helping us expand our services and streamline our operations. Their team brings valuable insight and ongoing improvements.
            </blockquote>
            <div class="author">
              <img src="/images/illustrations/faces/3.png" alt=""/>
              <h5>M. Pothunnah</h5>
              <span>Operations manager critical services</span>
            </div>
          </figure>
        </div>
      </div>
    </div>
  </div>
</section>
    

    
    

<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<style>
  .grecaptcha-badge {
    visibility: hidden;
  }
</style>

<section class="section section-light-grey is-medium" id="section5">
  <div class="container">
    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2 is-spaced">Drop us a line or two</h2>
      <h3 class="subtitle is-5 is-muted">We&#39;d love to hear from you</h3>
      <div class="divider is-centered"></div>
    </div>

    <div class="content-wrapper">
      <div class="columns">
        <div class="column is-6 is-offset-3">
          
          <div
            id="form-success"
            class="notification is-success"
            style="display: none"
          >
            <button
              class="delete"
              onclick="document.getElementById('form-success').style.display='none'"
            ></button>
            <strong>Thank you!</strong> Your message has been sent successfully.
            We'll get back to you soon.
          </div>

          
          <div
            id="form-error"
            class="notification is-danger"
            style="display: none"
          >
            <button
              class="delete"
              onclick="document.getElementById('form-error').style.display='none'"
            ></button>
            There was a problem submitting your form. Please try again.
          </div>

          <form
            id="contact-form"
            
            action="https://formspree.io/f/xgvkjykj"
            
            method="POST"
            
          >
            <div class="columns is-multiline">
              <div class="column is-6">
                <input
                  class="input is-medium"
                  name="name"
                  type="text"
                  placeholder="Enter your name"
                  required
                />
              </div>
              <div class="column is-6">
                <input
                  class="input is-medium"
                  name="email"
                  type="email"
                  placeholder="Enter your email address"
                  required
                />
              </div>
              <div class="column is-12">
                <input
                  class="input is-medium"
                  name="subject"
                  type="text"
                  placeholder="Subject"
                />
              </div>
              <div class="column is-12">
                <textarea
                  class="textarea"
                  name="message"
                  rows="10"
                  placeholder="Write something..."
                  required
                ></textarea>
              </div>
              
              <input type="text" name="_gotcha" style="display: none" />
              <div class="form-footer has-text-centered mt-10">
                <button
                  id="submit-button"
                  class="button cta is-large primary-btn raised is-clear g-recaptcha"
                  data-sitekey="6Lee40grAAAAADsjLzXnJPcvrYVKTUObzRUBbemn"
                  data-callback="onSubmit"
                >
                  Send Message
                </button>
              </div>
              <div class="column is-12 has-text-centered">
                <p class="is-size-7 has-text-grey">
                  This site is protected by reCAPTCHA and the Google
                  <a href="https://policies.google.com/privacy" target="_blank">Privacy Policy</a> and
                  <a href="https://policies.google.com/terms" target="_blank">Terms of Service</a> apply.
                </p>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  var form, submitButton, successMessage, errorMessage, hasRecaptcha

  document.addEventListener("DOMContentLoaded", function () {
    form = document.getElementById("contact-form");
    submitButton = document.getElementById("submit-button");
    successMessage = document.getElementById("form-success");
    errorMessage = document.getElementById("form-error");
    hasRecaptcha = submitButton.classList.contains('g-recaptcha');

    
    if (!hasRecaptcha) {
      form.addEventListener("submit", handleSubmit);
    }
  });

  async function submitForm() {
    
    successMessage.style.display = "none";
    errorMessage.style.display = "none";

    
    var originalButtonText = submitButton.textContent;
    submitButton.textContent = "Sending...";
    submitButton.disabled = true;

    
    var data = new FormData(form);

    try {
      console.log("Submitting form data:", data);
      console.log("Form action:", form.action);

      const response = await fetch(form.action, {
        method: form.method,
        body: data,
        headers: {
          Accept: "application/json",
        },
      });

      console.log("Response status:", response.status);

      if (response.ok) {
        successMessage.style.display = "block";
        form.reset();
        
        successMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      } else {
        
        const errorData = await response.text();
        console.error("Form submission error:", response.status, errorData);
        errorMessage.style.display = "block";
        errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    } catch (error) {
      console.error("Network error:", error);
      errorMessage.style.display = "block";
      errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    
    submitButton.textContent = originalButtonText;
    submitButton.disabled = false;
  }

  function handleSubmit(event) {
    event.preventDefault();

    
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }

    console.log("No reCAPTCHA, submitting directly...");
    
    submitForm();
  }

  
  function onSubmit(token) {
    
    console.log("reCAPTCHA success, token received:", token);
    
    if (!form.checkValidity()) {
      form.reportValidity();
      return;
    }
    
    submitForm();
  }
</script>

    

    
    
<footer class="footer footer-dark">
  <div class="container">
    <div class="columns">
      <div class="column">
        <div class="footer-logo">
          <img src="/images/logos/fresh-white-alt.svg">
        </div>
      </div>
        <div class="column">
          <div class="footer-column">
            <div class="footer-header">
                <h3>Product</h3>
            </div>
            <ul class="link-list">
              <li>
                <a href="/">
                  Discover features
                </a>
              </li>
              <li>
                <a href="/">
                  Why choose our product?
                </a>
              </li>
              <li>
                <a href="/">
                  Compare features
                </a>
              </li>
              <li>
                <a href="/">
                  Our roadmap
                </a>
              </li>
              <li>
                <a href="/agb">
                  AGB
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div class="column">
          <div class="footer-column">
            <div class="footer-header">
                <h3>Docs</h3>
            </div>
            <ul class="link-list">
              <li>
                <a href="/">
                  Get started
                </a>
              </li>
              <li>
                <a href="/">
                  User guides
                </a>
              </li>
              <li>
                <a href="/">
                  Admin guide
                </a>
              </li>
              <li>
                <a href="/">
                  Developers
                </a>
              </li>
            </ul>
          </div>
        </div>
        <div class="column">
          <div class="footer-column">
            <div class="footer-header">
                <h3>Blog</h3>
            </div>
            <ul class="link-list">
              <li>
                <a href="/blog/first">
                  Latest news
                </a>
              </li>
              <li>
                <a href="/blog/second">
                  Tech articles
                </a>
              </li>
            </ul>
          </div>
        </div>
      <div class="column">
        <div class="footer-column">
          <div class="footer-header">
            <h3>Follow Us</h3>
            <nav class="level is-mobile">
              <div class="level-left">
                <a class="level-item" href="https://github.com/StefMa">
                  <span class="icon"><i class="fa fa-github"></i></span>
                </a>
                <a class="level-item" href="https://dribbble.com/#">
                  <span class="icon"><i class="fa fa-dribbble"></i></span>
                </a>
                <a class="level-item" href="https://facebook.com/#">
                  <span class="icon"><i class="fa fa-facebook"></i></span>
                </a>
                <a class="level-item" href="https://twitter.com/StefMa91">
                  <span class="icon"><i class="fa fa-twitter"></i></span>
                </a>
                <a class="level-item" href="https://bitbucket.org/#">
                  <span class="icon"><i class="fa fa-bitbucket"></i></span>
                </a>
              </div>
            </nav>
            <a href="https://bulma.io" target="_blank">
              <img src="/images/logos/made-with-bulma.png" alt="Made with Bulma" width="128" height="24">
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>

    

    
    


    
    <div id="backtotop"><a href="#"></a></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
<script src="https://unpkg.com/feather-icons"></script>
<script src="/js/fresh.js"></script>
<script src="/js/jquery.panelslider.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/modernizr/2.8.3/modernizr.min.js"></script>

  </body>
</html>
